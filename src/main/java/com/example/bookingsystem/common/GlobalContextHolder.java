package com.example.bookingsystem.common;

import java.util.Objects;

public class GlobalContextHolder {

    private static final GlobalContextHolder INSTANCE = new GlobalContextHolder();

    private GlobalContextHolder() {
    }

    private static final ThreadLocal<Lang> LANG = ThreadLocal.withInitial(() -> Lang.UZ);
    private static final ThreadLocal<String> TRACE_ID = ThreadLocal.withInitial(String::new);
    private static final ThreadLocal<String> IP_ADDRESS = ThreadLocal.withInitial(String::new);

    public static Lang getLang() {
        final Lang lang = GlobalContextHolder.LANG.get();
        return Objects.isNull(lang) ? Lang.UZ : lang;
    }

    public static void setLang(Lang lang) {
        GlobalContextHolder.LANG.set(lang);
    }

    public static void setTraceId(String traceId) {
        GlobalContextHolder.TRACE_ID.set(traceId);
    }

    public static String getIpAddress() {
        return GlobalContextHolder.IP_ADDRESS.get();
    }

    public static void setIpAddress(String ipAddress) {
        GlobalContextHolder.IP_ADDRESS.set(ipAddress);
    }

    public static String getTraceId() {
        return GlobalContextHolder.TRACE_ID.get();
    }

}