package com.example.bookingsystem.security.permission;

import com.example.bookingsystem.common.response_factory.ResponseFactory;
import com.example.bookingsystem.exception.CustomException;
import com.example.bookingsystem.security.constant.Authority;
import com.example.bookingsystem.security.permission.specification.PermissionSpecification;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.example.bookingsystem.security.permission.specification.PermissionSpecification.byAuthority;
import static com.example.bookingsystem.security.permission.specification.PermissionSpecification.byEnabled;
import static com.example.bookingsystem.security.permission.specification.PermissionSpecification.byName;
import static java.lang.String.format;

@Service
public class PermissionService {

    private final PermissionService self;
    private final PermissionRepository permissionRepository;
    private final ResponseFactory responseFactory;

    public PermissionService(PermissionService self, PermissionRepository permissionRepository, ResponseFactory responseFactory) {
        this.self = self;
        this.permissionRepository = permissionRepository;
        this.responseFactory = responseFactory;
    }

    @Transactional
    public ResponseEntity<?> findAll(Integer page, Integer size, String name, Authority authority, Boolean enabled) {
        Specification<Permission> specification = Specification.where(byName(name))
                .and(byAuthority(authority))
                .and(byEnabled(enabled));
        Pageable pageable = PageRequest.of(page, size, Sort.by( Sort.Direction.DESC, "updatedAt"));
        Page<Permission> permissions = permissionRepository.findAll(specification, pageable);
        return responseFactory.success(new PageImpl<>(permissions.getContent(), pageable, permissions.getTotalElements()));
    }

    public Permission findByIdOrThrow(Long permissionId) {
        return permissionRepository.findById(permissionId)
                .orElseThrow(() -> new CustomException(
                        responseFactory.noDataFound(format("Permission not found. Id: %s", permissionId)))
                );
    }

    @Transactional
    public void save(PermissionDTO permissionDTO) {
        Permission permission = Permission.builder()
                .name(permissionDTO.name())
                .description(permissionDTO.description())
                .authority(permissionDTO.authority())
                .enabled(permissionDTO.enabled())
                .build();
        permissionRepository.save(permission);
    }

    @Transactional
    public void update(PermissionDTO permissionDTO) {
        Permission permission = self.findByIdOrThrow(permissionDTO.id());
        permission.setName(permissionDTO.name());
        permission.setDescription(permissionDTO.description());
        permission.setAuthority(permissionDTO.authority());
        permissionRepository.save(permission);
    }

    @Transactional
    public void disablePermission(Long permissionId) {
        Permission permission = self.findByIdOrThrow(permissionId);
        permission.setEnabled(false);
        permissionRepository.save(permission);
    }

}