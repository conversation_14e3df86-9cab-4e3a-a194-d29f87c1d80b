package com.example.bookingsystem.security.permission;

import com.example.bookingsystem.security.constant.Authority;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Transient;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "permissions")
public class Permission {

    @Transient
    private static final String PERMISSIONS_SEQUENCE = "seq_permissions_id";

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = PERMISSIONS_SEQUENCE
    )
    @SequenceGenerator(
            name = PERMISSIONS_SEQUENCE,
            sequenceName = PERMISSIONS_SEQUENCE,
            allocationSize = 1
    )
    private Long id;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "authority")
    @Enumerated(EnumType.STRING)
    private Authority authority;

    @Column(name = "description")
    private String description;

    @Column(name = "enabled", columnDefinition = "boolean default true")
    private Boolean enabled;

}