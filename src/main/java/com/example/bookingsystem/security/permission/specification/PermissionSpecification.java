package com.example.bookingsystem.security.permission.specification;

import com.example.bookingsystem.security.constant.Authority;
import com.example.bookingsystem.security.permission.Permission;
import lombok.experimental.UtilityClass;
import org.springframework.data.jpa.domain.Specification;

@UtilityClass
public class PermissionSpecification {

    public static Specification<Permission> byName(String name) {
        return ((root, query, criteriaBuilder) -> {
            if (name == null) {
                return criteriaBuilder.conjunction();
            }
            return criteriaBuilder.equal(root.get("name"), name);
        });
    }

    public static Specification<Permission> byAuthority(Authority authority) {
        return (root, query, criteriaBuilder) -> {
            if (authority == null) {
                return criteriaBuilder.conjunction();
            }
            return criteriaBuilder.equal(root.get("authority"), authority);
        };
    }

    public static Specification<Permission> byEnabled(Boolean enabled) {
        return (root, query, criteriaBuilder) -> {
            if (enabled == null) {
                return criteriaBuilder.conjunction();
            }
            return criteriaBuilder.equal(root.get("enabled"), enabled);
        };
    }

}