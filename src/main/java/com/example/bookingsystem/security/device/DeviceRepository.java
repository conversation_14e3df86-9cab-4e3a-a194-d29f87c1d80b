package com.example.bookingsystem.security.device;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Optional;

public interface DeviceRepository extends JpaRepository<Device, Long> {

    @Query("select d from devices d where devices.deviceId = :deviceId and d.userId = :userId")
    Optional<Device> findByDeviceIdAndUserId(String deviceId, Long userId);

}