package com.example.bookingsystem.security.device;

import com.example.bookingsystem.common.response_factory.ResponseFactory;
import com.example.bookingsystem.exception.CustomException;
import com.example.bookingsystem.security.device.platform.Platform;
import com.example.bookingsystem.security.jwt.JwtProvider;
import com.example.bookingsystem.user.User;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

import static com.example.bookingsystem.common.HeaderConstants.DEVICE_ID;
import static com.example.bookingsystem.common.HeaderConstants.DEVICE_NAME;
import static com.example.bookingsystem.common.HeaderConstants.X_REAL_IP;

@Service
public class DeviceService {

    private final DeviceRepository deviceRepository;
    private final JwtProvider jwtProvider;
    private final ResponseFactory responseFactory;

    public DeviceService(
            DeviceRepository deviceRepository,
            JwtProvider jwtProvider,
            ResponseFactory responseFactory
    ) {
        this.deviceRepository = deviceRepository;
        this.jwtProvider = jwtProvider;
        this.responseFactory = responseFactory;
    }

    @Transactional
    public Device save(DeviceDTO deviceDTO) {
        Device device = new Device();
        BeanUtils.copyProperties(deviceDTO, device);
        return deviceRepository.save(device);
    }

    @Transactional
    public Device save(User user, HttpServletRequest request) {
        final String accessToken = jwtProvider.generateAccessToken(user);
        final String refreshToken = jwtProvider.generateRefreshToken(user);
        final String ipAddress = request.getHeader(X_REAL_IP);
        final String deviceId = request.getHeader(DEVICE_ID);
        final Platform platform = Platform.findByName(request.getHeader(DEVICE_NAME));
        if (Objects.isNull(platform)) {
            throw new CustomException(responseFactory.noDataFound("Platform not found"));
        }
        if (!validation(user.getId(), deviceId, ipAddress)) {
            throw new CustomException(responseFactory.internalServerError());
        }
        Device device = deviceRepository.findByDeviceIdAndUserId(deviceId, user.getId()).orElse(null);
        if (Objects.isNull(device)) {
            device = save(new DeviceDTO(user.getId(), deviceId, platform, ipAddress));
        }
        device.setAccessToken(accessToken);
        device.setRefreshToken(refreshToken);
        device.setAccessTokenExpired(false);
        device.setDeviceId(deviceId);
        device.setIpAddress(ipAddress);
        return deviceRepository.save(device);
    }

    private boolean validation(Long userId, String deviceId, String ipAddress) {
        return userId != null || deviceId != null || ipAddress != null;
    }

}