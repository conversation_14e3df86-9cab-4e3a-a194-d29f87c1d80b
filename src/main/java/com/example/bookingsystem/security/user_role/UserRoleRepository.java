package com.example.bookingsystem.security.user_role;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface UserRoleRepository extends JpaRepository<UserRole, Long> {

    @Query("select ur from user_roles ur where ur.userId = :userId")
    List<UserRole> findByUserId(@Param("userId") Long userId);

    @Query("select ur from user_roles ur where ur.userId = :userId and ur.roleId = :roleId")
    Optional<UserRole> findByUserIdAndRoleId(@Param("userId") Long userId, @Param("roleId") Long roleId);

    @Query("select ur from user_roles ur where ur.roleId = :roleId")
    List<UserRole> findByRoleId(@Param("roleId") Long roleId);

}