package com.example.bookingsystem.security.user_role;

import com.example.bookingsystem.security.role.Role;
import com.example.bookingsystem.user.User;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Transient;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "user_roles")
public class UserRole {

    @Transient
    private static final String USER_ROLES_SEQUENCE = "seq_user_roles_id";

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = USER_ROLES_SEQUENCE
    )
    @SequenceGenerator(
            name = USER_ROLES_SEQUENCE,
            sequenceName = USER_ROLES_SEQUENCE,
            allocationSize = 1
    )
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    private User user;

    @Column(name = "user_id")
    private Long userId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "role_id")
    private Role role;

    @Column(name = "role_id")
    private Long roleId;

}