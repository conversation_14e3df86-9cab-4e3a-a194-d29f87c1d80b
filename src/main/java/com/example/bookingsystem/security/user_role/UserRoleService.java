package com.example.bookingsystem.security.user_role;

import com.example.bookingsystem.common.response_factory.ResponseFactory;
import com.example.bookingsystem.exception.CustomException;
import com.example.bookingsystem.security.role.Role;
import com.example.bookingsystem.security.role.RoleDTO;
import com.example.bookingsystem.security.role.RoleService;
import com.example.bookingsystem.user.User;
import com.example.bookingsystem.user.UserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

import static java.lang.String.format;

@Service
public class UserRoleService {

    private final UserRoleService self;
    private final UserRoleRepository userRoleRepository;
    private final RoleService roleService;
    private final UserService userService;
    private final ResponseFactory responseFactory;

    public UserRoleService(
            UserRoleService self,
            UserRoleRepository userRoleRepository,
            RoleService roleService,
            UserService userService,
            ResponseFactory responseFactory
    ) {
        this.self = self;
        this.userRoleRepository = userRoleRepository;
        this.roleService = roleService;
        this.userService = userService;
        this.responseFactory = responseFactory;
    }

    public UserRole findByIdOrThrow(Long userRoleId) {
        return userRoleRepository.findById(userRoleId).orElseThrow(
                () -> new CustomException(
                        responseFactory.noDataFound(format("UserRole not found. Id: %s", userRoleId))
                )
        );
    }

    public List<UserRole> findByUserId(Long userId) {
        return userRoleRepository.findByUserId(userId);
    }

    public List<UserRole> findByRoleId(Long roleId) {
        return userRoleRepository.findByRoleId(roleId);
    }

    @Transactional
    public void save(UserRoleDTO userRoleDTO) {
        User user = userService.findByIdOrThrow(userRoleDTO.userId());
        Role role = roleService.findByIdOrThrow(userRoleDTO.roleId());
        Optional<UserRole> userRoleOptional = userRoleRepository.findByUserIdAndRoleId(userRoleDTO.userId(), userRoleDTO.roleId());
        if (userRoleOptional.isPresent()) {
            throw new CustomException(responseFactory.dataExists(format("UserRole exists: UserId: %s, RoleId: %s", userRoleDTO.userId(), userRoleDTO.roleId())));
        }
        UserRole userRole = UserRole.builder()
                .userId(user.getId())
                .roleId(role.getId())
                .build();
        userRoleRepository.save(userRole);
    }

    @Transactional
    public void update(UserRoleDTO userRoleDTO) {
        UserRole userRole = self.findByIdOrThrow(userRoleDTO.id());
        userRole.setRoleId(userRoleDTO.roleId());
    }

    @Transactional
    public void deleteByRoleId(Long roleId) {
        List<UserRole> userRoleList = findByRoleId(roleId);
        userRoleRepository.deleteAll(userRoleList);
    }

    @Transactional
    public UserRolesDTO getUserRoles(Long userId) {
        User user = userService.findByIdOrThrow(userId);
        List<UserRole> userRoles = self.findByUserId(userId);
        List<Long> rolesIds = userRoles.stream().map(UserRole::getRoleId).toList();
        List<RoleDTO> roles = roleService.findAllByIds(rolesIds);
        return UserRolesDTO.builder()
                .id(user.getId())
                .name(user.getName())
                .phoneNumber(user.getPhoneNumber())
                .roles(roles)
                .build();
    }

}