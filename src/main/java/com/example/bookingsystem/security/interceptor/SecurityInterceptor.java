package com.example.bookingsystem.security.interceptor;

import brave.Span;
import brave.Tracer;
import com.example.bookingsystem.common.GlobalContextHolder;
import com.example.bookingsystem.common.Lang;
import com.example.bookingsystem.common.LoggingConstants;
import com.example.bookingsystem.common.response_factory.ResponseFactory;
import com.example.bookingsystem.exception.CustomException;
import com.example.bookingsystem.security.device.platform.Platform;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.servlet.HandlerInterceptor;

import static com.example.bookingsystem.common.HeaderConstants.DEVICE_ID;
import static com.example.bookingsystem.common.HeaderConstants.DEVICE_NAME;
import static com.example.bookingsystem.common.HeaderConstants.USER_LANG;
import static com.example.bookingsystem.common.HeaderConstants.X_REAL_IP;
import static com.example.bookingsystem.common.LoggingConstants.IP_ADDRESS;
import static com.example.bookingsystem.common.LoggingConstants.LANGUAGE;
import static com.example.bookingsystem.common.LoggingConstants.REQUEST_METHOD;
import static com.example.bookingsystem.common.LoggingConstants.TRACE_ID;
import static com.example.bookingsystem.security.config.SecurityConfig.AUTH_WHITELIST;
import static com.example.bookingsystem.security.device.platform.Platform.WEB;

@Component
@RequiredArgsConstructor
public class SecurityInterceptor implements HandlerInterceptor {

    private Tracer tracer;
    private final ResponseFactory responseFactory;

    @Override
    public boolean preHandle(
            @NonNull HttpServletRequest request,
            @NonNull HttpServletResponse response,
            @NonNull Object handler
    ) throws Exception {
        final String deviceId = request.getHeader(DEVICE_ID);
        String ipAddress = request.getHeader(X_REAL_IP);
        final Platform platform = Platform.findByName(request.getHeader(DEVICE_NAME));
        final Lang lang = Lang.findByName(request.getHeader(USER_LANG));
        final var traceId = getTraceId();
        GlobalContextHolder.setTraceId(traceId);
        MDC.put(TRACE_ID, traceId);
        MDC.put(REQUEST_METHOD, request.getMethod());
        GlobalContextHolder.setLang(lang);
        if (isAllowedWithoutSecurityCheck(request)) {
            return true;
        }
        if (isValidHeaders(ipAddress, deviceId, platform, lang)) {
            throw new CustomException(responseFactory.internalServerError());
        }
        ipAddress = platform.equals(WEB) ? request.getRemoteAddr() : ipAddress;
        GlobalContextHolder.setIpAddress(ipAddress);
        MDC.put(IP_ADDRESS, ipAddress);
        MDC.put(LANGUAGE, lang.name());
        return true;
    }

    public boolean isAllowedWithoutSecurityCheck(HttpServletRequest request) {
        for (String allowedPath : AUTH_WHITELIST) {
            if (new AntPathMatcher().match(allowedPath, request.getRequestURI())) {
                return true;
            }
        }
        return false;
    }

    private boolean isValidHeaders(final String ipAddress,
                                   final String deviceId,
                                   final Platform platform,
                                   final Lang lang) {
        return ipAddress == null && deviceId == null && platform == null && lang == null;
    }

    @Override
    public void afterCompletion(@NonNull HttpServletRequest request,
                                @NonNull HttpServletResponse response,
                                @NonNull Object handler,
                                Exception ex) throws Exception {
        HandlerInterceptor.super.afterCompletion(request, response, handler, ex);
    }

    private String getTraceId() {
        Span span = tracer.currentSpan();
        return span.context().traceIdString();
    }

}